<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/explore.css">
    <title>Explore Music</title>
</head>

<body>
    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>
            <ul class="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html" aria-current="page">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu"
                    aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main aria-label="Explore Music Main Content">
        <section class="explore-hero" aria-label="Explore Hero Section">
            <div class="hero-content">
                <h1>Explore Music</h1>
                <p class="hero-subtitle">Discover new artists, genres, and tracks tailored to your taste</p>
                <a href="#search" class="cta-button">Start Discovering</a>
            </div>
        </section>

        <section class="search-section" aria-label="Music Search and Filters">
            <div class="section-header">
                <h2>Discover Music</h2>
            </div>
            <form class="search-container" role="search" aria-label="Music Search">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon" aria-hidden="true"></i>
                    <input type="search" class="search-bar" placeholder="Search for artists, songs, or albums..."
                        aria-label="Search for music" minlength="2" required>
                    <button type="button" class="search-clear" aria-label="Clear search">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <button type="submit" class="btn-medium search-button">Search</button>
            </form>

            <div class="filters-container" aria-label="Music Filters">
                <select class="filter-select" aria-label="Filter by genre">
                    <option value="">Genre</option>
                    <option value="pop">Pop</option>
                    <option value="rock">Rock</option>
                    <option value="hiphop">Hip-Hop</option>
                    <option value="rnb">R&B</option>
                    <option value="jazz">Jazz</option>
                    <option value="electronic">Electronic</option>
                    <option value="classical">Classical</option>
                    <option value="indie">Indie</option>
                    <option value="metal">Metal</option>
                    <option value="blues">Blues</option>
                    <option value="country">Country</option>
                    <option value="folk">Folk</option>
                    <option value="reggae">Reggae</option>
                    <option value="latin">Latin</option>
                    <option value="soul">Soul</option>
                    <option value="funk">Funk</option>
                    <option value="punk">Punk</option>
                    <option value="ambient">Ambient</option>
                    <option value="edm">EDM</option>
                    <option value="trap">Trap</option>
                    <option value="house">House</option>
                    <option value="techno">Techno</option>
                    <option value="alternative">Alternative</option>
                    <option value="world">World</option>
                    <option value="instrumental">Instrumental</option>
                    <option value="soundtrack">Soundtrack</option>
                    <option value="gospel">Gospel</option>
                </select>
                <select class="filter-select" aria-label="Filter by popularity">
                    <option value="">Popularity</option>
                    <option value="most-popular">Most Popular</option>
                    <option value="trending">Trending</option>
                    <option value="new-releases">New Releases</option>
                </select>
                <select class="filter-select" aria-label="Filter by release date">
                    <option value="">Release Date</option>
                    <option value="today">Today</option>
                    <option value="this-week">This Week</option>
                    <option value="last-week">Last Week</option>
                    <option value="this-month">This Month</option>
                    <option value="last-month">Last Month</option>
                    <option value="last-3-months">Last 3 Months</option>
                    <option value="last-6-months">Last 6 Months</option>
                    <option value="this-year">This Year</option>
                    <option value="last-year">Last Year</option>
                    <option value="last-2-years">Last 2 Years</option>
                    <option value="last-5-years">Last 5 Years</option>
                    <option value="2010s">2010s</option>
                    <option value="2000s">2000s</option>
                    <option value="1990s">1990s</option>
                    <option value="1980s">1980s</option>
                    <option value="1970s">1970s</option>
                    <option value="older">Older</option>
                </select>
            </div>
            <div class="results-count" aria-live="polite"></div>
            <div class="results-container" id="resultsContainer">
                <span class="empty-results">Start typing to search for music.</span>
            </div>
        </section>

        <!-- Featured Artists Section -->
        <article class="section featured-artists" aria-labelledby="featured-artists-title">
            <div class="section-header">
                <h2 id="featured-artists-title">Featured Artists</h2>
                <p class="section-description">Discover talented artists making waves</p>
            </div>
            <div class="carousel-container" role="region" aria-label="Featured Artists Carousel" tabindex="0">
                <button class="carousel-button prev" id="carouselPrevBtn" aria-label="View previous artists"></button>
                <div class="carousel-track" id="carouselTrack">
                    <!-- Artist Card 1 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-01.png" alt="Featured Artist: Artist Alpha" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Artist Alpha">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Artist Alpha</h3>
                                    <p>Genre: Indie Pop</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                    <!-- Artist Card 2 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-02.png" alt="Featured Artist: Beta Beats" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Beta Beats">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Beta Beats</h3>
                                    <p>Genre: Lo-fi Hip Hop</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                    <!-- Artist Card 3 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-03.png" alt="Featured Artist: Gamma Grooves" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Gamma Grooves">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Gamma Grooves</h3>
                                    <p>Genre: Funk, Soul</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                    <!-- Artist Card 4 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-04.png" alt="Featured Artist: Delta Waves" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Delta Waves">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Delta Waves</h3>
                                    <p>Genre: Ambient Electronic</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                    <!-- Artist Card 5 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-05.png" alt="Featured Artist: Epsilon Echoes" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Epsilon Echoes">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Epsilon Echoes</h3>
                                    <p>Genre: Synthwave</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                    <!-- Artist Card 6 -->
                    <div class="carousel-card">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-06.png" alt="Featured Artist: Zeta Flow" loading="lazy" />
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="View Zeta Flow">
                                        <i class="fas fa-user"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Zeta Flow</h3>
                                    <p>Genre: Chillhop</p>
                                </div>
                                <a href="#" class="button">View Profile</a>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="carousel-button next" id="carouselNextBtn" aria-label="View next artists"></button>
            </div>
        </article>
        <!-- Popular Genres Section -->
        <article class="section popular-genres" aria-labelledby="popular-genres-title">
            <div class="section-header">
                <h2 id="popular-genres-title">Popular Genres</h2>
                <p class="section-description">Browse trending genres and discover new favorites</p>
            </div>
            <div class="carousel-container" role="region" aria-label="Popular Genres Carousel" tabindex="0">
                <button class="carousel-button prev" id="genreCarouselPrevBtn" aria-label="View previous genres"></button>
                <div class="carousel-track" id="genreCarouselTrack">
                    <!-- Example Genre Cards -->
                    <div class="carousel-card genre-card">
                        <div class="genre-icon"><i class="fas fa-music"></i></div>
                        <div class="genre-name">Pop</div>
                    </div>
                    <div class="carousel-card genre-card">
                        <div class="genre-icon"><i class="fas fa-guitar"></i></div>
                        <div class="genre-name">Rock</div>
                    </div>
                    <div class="carousel-card genre-card">
                        <div class="genre-icon"><i class="fas fa-headphones"></i></div>
                        <div class="genre-name">Hip-Hop</div>
                    </div>
                    <div class="carousel-card genre-card">
                        <div class="genre-icon"><i class="fas fa-drum"></i></div>
                        <div class="genre-name">EDM</div>
                    </div>
                    <div class="carousel-card genre-card">
                        <div class="genre-icon"><i class="fas fa-saxophone"></i></div>
                        <div class="genre-name">Jazz</div>
                    </div>
                    <div class="carousel-card genre-card">
                        <div class="genre-icon"><i class="fas fa-microphone"></i></div>
                        <div class="genre-name">R&B</div>
                    </div>
                    <div class="carousel-card genre-card">
                        <div class="genre-icon"><i class="fas fa-record-vinyl"></i></div>
                        <div class="genre-name">Indie</div>
                    </div>
                    <div class="carousel-card genre-card">
                        <div class="genre-icon"><i class="fas fa-bolt"></i></div>
                        <div class="genre-name">Electronic</div>
                    </div>
                    <div class="carousel-card genre-card">
                        <div class="genre-icon"><i class="fas fa-gem"></i></div>
                        <div class="genre-name">Soul</div>
                    </div>
                    <!-- Add more genres as needed -->
                </div>
                <button class="carousel-button next" id="genreCarouselNextBtn" aria-label="View next genres"></button>
            </div>
        </article>
        <!-- New Releases Section -->
        <article class="section new-releases" aria-labelledby="new-releases-title">
            <div class="section-header">
                <h2 id="new-releases-title">New Releases</h2>
                <p class="section-description">Check out the latest albums and singles</p>
            </div>
            <div class="carousel-container" role="region" aria-label="New Releases Carousel" tabindex="0">
                <button class="carousel-button prev" id="newReleasesPrevBtn" aria-label="View previous releases"></button>
                <div class="carousel-track" id="newReleasesTrack">
                    <!-- Example New Release Cards -->
                    <div class="carousel-card release-card">
                        <div class="release-img">
                            <img src="imgs/album-07.png" alt="Album: Neon Nights" loading="lazy" />
                        </div>
                        <div class="release-info">
                            <div class="release-title">Neon Nights</div>
                            <div class="release-artist">by Luna Wave</div>
                        </div>
                    </div>
                    <div class="carousel-card release-card">
                        <div class="release-img">
                            <img src="imgs/album-08.png" alt="Album: Electric Dreams" loading="lazy" />
                        </div>
                        <div class="release-info">
                            <div class="release-title">Electric Dreams</div>
                            <div class="release-artist">by Synth City</div>
                        </div>
                    </div>
                    <div class="carousel-card release-card">
                        <div class="release-img">
                            <img src="imgs/album-09.png" alt="Album: Groove Machine" loading="lazy" />
                        </div>
                        <div class="release-info">
                            <div class="release-title">Groove Machine</div>
                            <div class="release-artist">by The Funksters</div>
                        </div>
                    </div>
                    <div class="carousel-card release-card">
                        <div class="release-img">
                            <img src="imgs/album-10.png" alt="Album: Chill Vibes" loading="lazy" />
                        </div>
                        <div class="release-info">
                            <div class="release-title">Chill Vibes</div>
                            <div class="release-artist">by Aurora Sky</div>
                        </div>
                    </div>
                    <!-- Add more releases as needed -->
                </div>
                <button class="carousel-button next" id="newReleasesNextBtn" aria-label="View next releases"></button>
            </div>
        </article>
        <!-- Top Playlists Section -->
        <article class="section top-playlists" aria-labelledby="top-playlists-title">
            <div class="section-header">
                <h2 id="top-playlists-title">Top Playlists</h2>
                <p class="section-description">Listen to curated and trending playlists</p>
            </div>
            <div class="carousel-container" role="region" aria-label="Top Playlists Carousel" tabindex="0">
                <button class="carousel-button prev" id="playlistsPrevBtn" aria-label="View previous playlists"></button>
                <div class="carousel-track" id="playlistsTrack">
                    <!-- Example Playlist Cards -->
                    <div class="carousel-card playlist-card">
                        <div class="playlist-img">
                            <img src="imgs/playlist-01.png" alt="Playlist: Chill Beats" loading="lazy" />
                        </div>
                        <div class="playlist-info">
                            <div class="playlist-title">Chill Beats</div>
                            <div class="playlist-desc">Relax &amp; study</div>
                        </div>
                    </div>
                    <div class="carousel-card playlist-card">
                        <div class="playlist-img">
                            <img src="imgs/playlist-02.png" alt="Playlist: Workout Hits" loading="lazy" />
                        </div>
                        <div class="playlist-info">
                            <div class="playlist-title">Workout Hits</div>
                            <div class="playlist-desc">Pump up your energy</div>
                        </div>
                    </div>
                    <div class="carousel-card playlist-card">
                        <div class="playlist-img">
                            <img src="imgs/playlist-03.png" alt="Playlist: Indie Gems" loading="lazy" />
                        </div>
                        <div class="playlist-info">
                            <div class="playlist-title">Indie Gems</div>
                            <div class="playlist-desc">Hidden treasures</div>
                        </div>
                    </div>
                    <div class="carousel-card playlist-card">
                        <div class="playlist-img">
                            <img src="imgs/playlist-04.png" alt="Playlist: Party Time" loading="lazy" />
                        </div>
                        <div class="playlist-info">
                            <div class="playlist-title">Party Time</div>
                            <div class="playlist-desc">Get the party started</div>
                        </div>
                    </div>
                    <!-- Add more playlists as needed -->
                </div>
                <button class="carousel-button next" id="playlistsNextBtn" aria-label="View next playlists"></button>
            </div>
        </article>
    </main>
    <!-- Mini Player -->
    <div class="mini-player hidden" id="miniPlayer">
        <div class="mini-player-content">
            <div class="mini-player-info">
                <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
                <div class="mini-player-text">
                    <h4 id="miniPlayerTitle">Track Title</h4>
                    <p id="miniPlayerArtist">Artist Name</p>
                </div>
            </div>
            <div class="mini-player-controls">
                <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                    <i class="fas fa-play"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>
            <div class="mini-player-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="miniProgressFill"></div>
                </div>
                <div class="time-display">
                    <span id="miniCurrentTime">0:00</span>
                    <span id="miniDuration">3:45</span>
                </div>
            </div>
            <div class="mini-player-actions">
                <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
    <script src="js/main.js"></script>
</body>

</html>