// Share modal open/close logic
document.addEventListener('DOMContentLoaded', function () {
    const shareBtn = document.querySelector('.share-btn');
    const shareModal = document.getElementById('shareModal');

    if (shareBtn && shareModal) {
        shareBtn.addEventListener('click', () => {
            shareModal.hidden = false;
        });

        // Close modal when clicking outside modal content
        shareModal.addEventListener('click', (e) => {
            if (e.target === shareModal) {
                shareModal.hidden = true;
            }
        });

        // Close modal on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !shareModal.hidden) {
                shareModal.hidden = true;
            }
        });
    }
});
