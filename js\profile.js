// Profile Page Interactive Features
class ProfileManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupProfileImageUpload();
        this.setupBioEditing();
        this.setupPlayButtons();
        this.setupGenreCards();
        this.setupShareProfile();
        this.animateStats();
    }

    setupProfileImageUpload() {
        const imageInput = document.getElementById('profileImageInput');
        const avatar = document.getElementById('profileHeroAvatar');

        if (imageInput && avatar) {
            imageInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (ev) => {
                        avatar.src = ev.target.result;
                        this.showNotification('Profile picture updated!', 'success');
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
    }

    setupBioEditing() {
        const bio = document.getElementById('profileHeroBio');
        if (bio) {
            const placeholder = 'Click here to add a short bio about yourself...';

            bio.addEventListener('focus', () => {
                if (bio.textContent === placeholder) {
                    bio.textContent = '';
                }
                bio.style.color = '#fff';
            });

            bio.addEventListener('blur', () => {
                if (bio.textContent.trim() === '') {
                    bio.textContent = placeholder;
                    bio.style.color = 'var(--text-secondary)';
                } else {
                    this.showNotification('Bio updated!', 'success');
                }
            });

            bio.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    bio.blur();
                }
            });
        }
    }

    setupPlayButtons() {
        // Handle favorite playlist/artist play buttons
        document.querySelectorAll('.favorite-play-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.playContent(btn);
            });
        });

        // Handle recent track play buttons
        document.querySelectorAll('.recent-track-play').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.playTrack(btn);
            });
        });
    }

    setupGenreCards() {
        document.querySelectorAll('.genre-card').forEach(card => {
            card.addEventListener('click', () => {
                const genre = card.dataset.genre;
                this.exploreGenre(genre);
            });

            // Add hover effect to progress bar
            card.addEventListener('mouseenter', () => {
                const progressFill = card.querySelector('.genre-progress-fill');
                if (progressFill) {
                    progressFill.style.background = 'linear-gradient(90deg, var(--cosmic-pink), var(--neon-blue))';
                }
            });

            card.addEventListener('mouseleave', () => {
                const progressFill = card.querySelector('.genre-progress-fill');
                if (progressFill) {
                    progressFill.style.background = 'linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink))';
                }
            });
        });
    }

    setupShareProfile() {
        const shareBtn = document.querySelector('.profile-share-btn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.shareProfile();
            });
        }
    }

    playContent(button) {
        const card = button.closest('.favorite-card');
        const title = card.querySelector('.favorite-card-title').textContent;

        // Add playing state
        button.innerHTML = '<i class="fas fa-pause"></i>';
        button.style.background = 'linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue))';

        this.showNotification(`Playing ${title}`, 'info');

        // Simulate play - reset after 3 seconds
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-play"></i>';
            button.style.background = 'var(--gradient-primary)';
        }, 3000);
    }

    playTrack(button) {
        const trackItem = button.closest('.recent-track-item');
        const title = trackItem.querySelector('h4').textContent;

        // Add playing state
        button.innerHTML = '<i class="fas fa-pause"></i>';
        button.style.background = 'var(--gradient-primary)';
        button.style.color = 'white';

        this.showNotification(`Playing ${title}`, 'info');

        // Simulate play - reset after 3 seconds
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-play"></i>';
            button.style.background = 'transparent';
            button.style.color = 'var(--neon-blue)';
        }, 3000);
    }

    exploreGenre(genre) {
        this.showNotification(`Exploring ${genre} music...`, 'info');
        // In a real app, this would navigate to genre exploration
        setTimeout(() => {
            window.location.href = `explore.html?genre=${genre}`;
        }, 1000);
    }

    shareProfile() {
        if (navigator.share) {
            navigator.share({
                title: 'Check out my Banshee Music Profile!',
                text: 'See what I\'ve been listening to on Banshee Music',
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                this.showNotification('Profile link copied to clipboard!', 'success');
            });
        }
    }

    animateStats() {
        const statNumbers = document.querySelectorAll('.profile-stat-number');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateNumber(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });

        statNumbers.forEach(stat => observer.observe(stat));
    }

    animateNumber(element) {
        const finalNumber = parseInt(element.textContent);
        const duration = 1000;
        const steps = 30;
        const increment = finalNumber / steps;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= finalNumber) {
                element.textContent = finalNumber;
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(current);
            }
        }, duration / steps);
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `profile-notification ${type}`;
        notification.textContent = message;

        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '100px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '600',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            background: type === 'success' ? 'linear-gradient(135deg, #4CAF50, #45a049)' :
                       type === 'error' ? 'linear-gradient(135deg, #f44336, #da190b)' :
                       'linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink))'
        });

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Initialize profile manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ProfileManager();
});
