:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    --player-bg: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%);
    --player-card: rgba(255,255,255,0.04);
    --player-border: 1.5px solid rgba(255,255,255,0.12);
    --player-radius: 22px;
    --player-shadow: 0 8px 32px rgba(56, 12, 97, 0.18);
    --player-glow: 0 0 24px 6px var(--neon-blue), 0 0 48px 12px var(--cosmic-pink);
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: #121212;
    color: var(--text-primary);
}

/* Profile Page Layout */
.profile-container {
    max-width: 700px;
    margin: 120px auto 40px auto;
    padding: 2rem 1rem;
    background: linear-gradient(135deg, #181c24 0%, #23243a 60%, #181c24 100%);
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(56, 12, 97, 0.18);
}

.profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.profile-section h1 {
    font-size: 2.2rem;
    font-weight: 800;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    margin-bottom: 1.5rem;
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 2rem;
    background: rgba(255,255,255,0.04);
    border-radius: 14px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.profile-avatar {
    width: 110px;
    height: 110px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    border: 3px solid var(--neon-blue);
}

.profile-details h2 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5em;
    color: var(--text-primary);
}

.profile-details p {
    font-size: 1rem;
    color: var(--text-secondary);
    margin: 0.2em 0;
}

/* Navbar Styles */
header {
    background: rgba(19, 21, 26, 0.95); /* Remove gradient, use solid/semi-transparent dark */
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    /* width: 100%; */ /* Redundant */
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;    
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative; /* For ::after positioning */
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative; /* For pseudo-element positioning */
    overflow: hidden;   /* To clip the pseudo-element with border-radius */
    z-index: 0;         /* Establish stacking context for ::before z-index */
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1; /* Place background behind the text */
    border-radius: inherit; /* Inherit parent's border-radius */
}

.dropdown a:hover {
    /* Background is now handled by ::before pseudo-element */
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1; /* Fade in the background */
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a; /* Or use your --error-color variable: var(--error-color); */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}



/* ===== PROFILE HERO SECTION ===== */
.profile-hero {
    position: relative;
    max-width: 1200px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    overflow: visible;
    padding: 3.5rem 2rem 2.5rem 2rem;
    margin-bottom: 2.5rem;
    margin-top: 110px;
    border-radius: 18px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    min-height: 260px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Animated background for hero */
.profile-hero-bg-animated {
    position: absolute;
    inset: 0;
    z-index: 0;
    pointer-events: none;
    border-radius: inherit;
    background: radial-gradient(circle at 60% 40%, rgba(111,0,255,0.10) 0%, transparent 70%),
                linear-gradient(120deg, rgba(0,224,255,0.10) 0%, rgba(255,0,110,0.10) 100%);
    animation: profileHeroBgMove 18s ease-in-out infinite alternate;
    opacity: 0.8;
}
@keyframes profileHeroBgMove {
    0% { background-position: 0% 0%, 0% 100%; }
    100% { background-position: 100% 100%, 100% 0%; }
}

.profile-hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 700px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.2rem;
}

.profile-image-form {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0.5em;
}

.profile-hero-avatar-label {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.profile-hero-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    border: 3px solid var(--neon-blue);
    margin-bottom: 0.2em;
    transition: filter 0.2s;
}
.profile-hero-avatar-label:hover .profile-hero-avatar,
.profile-hero-avatar-label:focus .profile-hero-avatar {
    filter: brightness(0.85) blur(1px);
}

.profile-avatar-edit {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: var(--gradient-primary);
    color: #fff;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    box-shadow: 0 2px 8px rgba(0,224,255,0.18);
    border: 2px solid #fff;
    transition: background 0.2s;
    pointer-events: none;
}
.profile-hero-avatar-label:hover .profile-avatar-edit,
.profile-hero-avatar-label:focus .profile-avatar-edit {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
}

.profile-settings-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255,255,255,0.08);
    border: none;
    color: var(--neon-blue);
    border-radius: 50%;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    z-index: 3;
}
.profile-settings-btn:hover,
.profile-settings-btn:focus {
    background: var(--gradient-primary);
    color: #fff;
    outline: none;
}

.profile-hero-bio {
    margin: 0.7em auto 0.2em auto;
    font-size: 1.1rem;
    color: var(--text-secondary);
    background: rgba(255,255,255,0.06);
    border-radius: 10px;
    padding: 0.7em 1.2em;
    min-height: 2.2em;
    max-width: 420px;
    outline: none;
    border: 1.5px solid transparent;
    transition: border 0.2s, background 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    text-align: center;
    cursor: text;
}
.profile-hero-bio:focus {
    border: 1.5px solid var(--cosmic-pink);
    background: rgba(255,255,255,0.13);
    color: #fff;
}

.profile-followed-by {
    margin: 0.5em auto 0.2em auto;
    color: var(--text-secondary);
    font-size: 1rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5em;
}
.profile-followed-by i {
    color: var(--neon-blue);
    margin-right: 0.2em;
}

/* Profile Stats Grid */
.profile-stats {
    display: flex;
    gap: 2.5rem;
    justify-content: center;
    margin-top: 1.2em;
    margin-bottom: 0.5em;
    z-index: 2;
}

.profile-stat-item {
    background: rgba(255,255,255,0.08);
    border-radius: 14px;
    padding: 1.2rem 2.2rem;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 110px;
    transition: transform 0.2s;
}

.profile-stat-item:hover {
    transform: translateY(-4px) scale(1.04);
    background: linear-gradient(90deg, rgba(0,224,255,0.13), rgba(255,0,110,0.10));
}

.profile-stat-icon {
    font-size: 2rem;
    margin-bottom: 0.4rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb),0.18);
    display: inline-block;
}

.profile-stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #fff;
    display: block;
    margin-bottom: 0.2rem;
    text-shadow: 0 0 12px rgba(255,255,255,0.7), 0 0 24px rgba(111,0,255,0.18);
}

.profile-stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 600;
    letter-spacing: 0.02em;
}

/* Responsive */
@media (max-width: 700px) {
    .profile-hero-content {
        gap: 0.7rem;
    }
    .profile-hero-title {
        font-size: 1.3rem;
    }
    .profile-hero-avatar {
        width: 60px;
        height: 60px;
    }
    .profile-stats {
        gap: 1rem;
    }
    .profile-stat-item {
        padding: 0.7rem 1rem;
        min-width: 70px;
    }
    .profile-stat-icon {
        font-size: 1.3rem;
    }
    .profile-stat-number {
        font-size: 1.1rem;
    }
    .profile-stat-label {
        font-size: 0.9rem;
    }
}

@media (max-width: 900px) {
    .profile-hero-header {
        flex-direction: column;
        gap: 1.2rem;
        align-items: center;
    }
    .profile-hero-userinfo {
        align-items: center;
    }
    .profile-hero-title-row {
        flex-direction: column;
        gap: 0.3em;
    }
}
@media (max-width: 600px) {
    .profile-hero {
        padding: 2rem 0.5rem 1.2rem 0.5rem;
        min-height: 120px;
    }
    .profile-hero-header {
        gap: 0.7rem;
    }
    .profile-hero-avatar {
        width: 60px;
        height: 60px;
    }
    .profile-hero-title {
        font-size: 1.3rem;
    }
}

/* Profile Header */
.profile-header {
    display: flex;
    gap: 2.5rem;
    align-items: flex-start;
    padding: 2.5rem;
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    width: 100%;
    max-width: 800px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.profile-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

/* Add backdrop gradient */
.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 50%,
        rgba(var(--electric-violet-rgb), 0.05) 100%
    );
    z-index: -1;
    opacity: 0.5;
}

/* Profile Image Section */
.profile-image-container {
    position: relative;
    width: 140px;
    height: 140px;
    border-radius: 50%;
}

.profile-image-container img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.profile-image-container:hover img {
    transform: scale(1.02);
}

/* Edit Image Button */
.edit-image-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--button-gradient);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--button-shadow);
    transition: all 0.3s ease;
    z-index: 10;
}

.edit-image-btn i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 1.2rem;
    line-height: 1;
}

.edit-image-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--button-hover-shadow);
}

/* Profile Info */
.profile-info {
    flex: 1;
    padding-top: 0.5rem;
}

.profile-name {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    background: linear-gradient(
        45deg,
        var(--neon-blue),
        var(--cosmic-pink) 50%,
        var(--electric-violet)
    );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    letter-spacing: -0.5px;
}

.profile-bio {
    font-size: 1.05rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.75rem;
    line-height: 1.8;
    max-width: 90%;
    position: relative;
    overflow-y: auto;
    max-height: 120px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for the profile bio */
.profile-bio::-webkit-scrollbar {
    width: 6px;
}

.profile-bio::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.profile-bio::-webkit-scrollbar-thumb {
    background: var(--neon-blue);
    border-radius: 3px;
}

.profile-bio::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(var(--neon-blue), var(--cosmic-pink));
}

/* Profile Stats */
.profile-stats {
    display: flex;
    gap: 2.5rem;
    margin-top: 1rem;
    position: relative;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    padding: 1rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    min-width: 110px;
}

.stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
}

.stat::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
}

.stat:hover::before {
    transform: scaleX(1);
}

.stat-value {
    font-size: 1.6rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.stat-icon {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 0.5rem;
    transition: transform 0.3s ease;
}

.stat:hover .stat-icon {
    transform: scale(1.2);
    color: white;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Edit Profile Button */
.edit-profile-btn {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: var(--button-gradient);
    border: none;
    color: white;
    padding: 0.4rem 0.9rem;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.edit-profile-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--button-hover-shadow);
}

/* ===== PROFILE CONTENT SECTION ===== */
.profile-content {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.content-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.content-section:hover {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

/* Profile Favorite Section */
.profile-favorite-section {
    margin: 2.5rem auto 1.5rem auto;
    max-width: 700px;
    background: rgba(255,255,255,0.04);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    padding: 2rem 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.2rem;
}

.favorite-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: linear-gradient(120deg, rgba(0,224,255,0.07) 0%, rgba(255,0,110,0.07) 100%);
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.10);
    padding: 1.2rem 1.5rem;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    position: relative;
    transition: box-shadow 0.25s, transform 0.25s, background 0.25s;
    cursor: pointer;
    outline: none;
}
.favorite-card:focus,
.favorite-card:hover {
    box-shadow: 0 8px 24px rgba(0,224,255,0.13), 0 2px 10px rgba(255,0,110,0.10);
    background: linear-gradient(120deg, rgba(0,224,255,0.13) 0%, rgba(255,0,110,0.13) 100%);
    transform: translateY(-4px) scale(1.02);
    z-index: 2;
}

.favorite-img-container {
    position: relative;
    width: 90px;
    height: 90px;
    border-radius: 14px;
    overflow: hidden;
    flex-shrink: 0;
    background: rgba(0,0,0,0.13);
    display: flex;
    align-items: center;
    justify-content: center;
}
.favorite-cover-img,
.favorite-artist-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.13);
    background: #181c24;
    transition: filter 0.2s;
}
.artist-avatar-container {
    border-radius: 50%;
    width: 90px;
    height: 90px;
}
.favorite-artist-avatar {
    border-radius: 50%;
    background: #181c24;
}

.favorite-play-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0,0,0,0.38);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.25s;
    z-index: 2;
    border-radius: inherit;
}
.favorite-card:hover .favorite-play-overlay,
.favorite-card:focus .favorite-play-overlay {
    opacity: 1;
    pointer-events: auto;
}
.favorite-play-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,224,255,0.18);
    cursor: pointer;
    transition: background 0.2s, transform 0.2s;
}
.favorite-play-btn:hover,
.favorite-play-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    transform: scale(1.1);
    outline: none;
}

.favorite-card-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3em;
    flex: 1;
}

.favorite-card-title {
    font-size: 1.15rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 0.2em;
    letter-spacing: 0.01em;
}

.favorite-card-meta {
    display: flex;
    gap: 1em;
    align-items: center;
    font-size: 0.98rem;
    color: var(--text-secondary);
    margin-bottom: 0.5em;
    flex-wrap: wrap;
}
.favorite-card-meta i {
    color: var(--neon-blue);
    margin-right: 0.3em;
}
.favorite-card-tag {
    background: rgba(0,224,255,0.09);
    color: var(--neon-blue);
    border-radius: 12px;
    padding: 0.15em 0.7em;
    font-size: 0.93em;
    font-weight: 600;
    letter-spacing: 0.01em;
}

.favorite-action-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 22px;
    padding: 0.5em 1.2em;
    font-size: 1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5em;
    cursor: pointer;
    box-shadow: var(--shadow-button);
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
    text-decoration: none;
    margin-top: 0.5em;
}
.favorite-action-btn:hover,
.favorite-action-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.05);
}

/* Section title refinement */
.profile-section-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5em;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Responsive adjustments */
@media (max-width: 700px) {
    .favorite-card {
        flex-direction: column;
        align-items: center;
        padding: 1rem 0.5rem;
        gap: 0.7rem;
        max-width: 98vw;
    }
    .favorite-img-container,
    .artist-avatar-container {
        width: 60px;
        height: 60px;
    }
    .favorite-card-title {
        font-size: 1rem;
    }
    .favorite-card-meta {
        font-size: 0.93rem;
        gap: 0.5em;
    }
    .favorite-card-tag {
        font-size: 0.9em;
    }
    .favorite-action-btn {
        font-size: 0.95em;
        padding: 0.4em 1em;
    }
}

/* ...existing code... */
