:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: #121212;
    color: var(--text-primary); /* Fixed: use --text-primary */
}

/* Navbar Styles */
header {
     background: linear-gradient(
        90deg,
        rgba(19, 21, 26, 0.95) 0%,
        rgba(26, 29, 36, 0.92) 60%,
        rgba(27, 27, 27, 0.1) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    /* width: 100%; */ /* Redundant */
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;    
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative; /* For ::after positioning */
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative; /* For pseudo-element positioning */
    overflow: hidden;   /* To clip the pseudo-element with border-radius */
    z-index: 0;         /* Establish stacking context for ::before z-index */
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1; /* Place background behind the text */
    border-radius: inherit; /* Inherit parent's border-radius */
}

.dropdown a:hover {
    /* Background is now handled by ::before pseudo-element */
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1; /* Fade in the background */
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a; /* Or use your --error-color variable: var(--error-color); */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}




.hero {
    background: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%);
    min-height: 90vh; /* Changed from 100vh to 90vh */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 120px 20px 60px;
    position: relative;
    overflow: hidden;
    margin-top: -80px; /* Offset for fixed header */
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(var(--electric-violet-rgb), 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.hero-content {
    max-width: 800px;
    width: 100%;
    position: relative;
    z-index: 2;
    text-align: center;
}

.stars {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.star {
    position: absolute;
    background: var(--text-primary);
    width: 2px;
    height: 2px;
    border-radius: 50%;
    animation: shimmer var(--shimmer-duration, 4s) infinite var(--shimmer-delay, 0s);
}

/* Simplified star positions with shimmer effect */
.star:nth-child(4n + 1) { top: 15%; left: 10%; --shimmer-delay: 0s; }
.star:nth-child(4n + 2) { top: 25%; left: 35%; --shimmer-delay: 1s; }
.star:nth-child(4n + 3) { top: 45%; left: 20%; --shimmer-delay: 2s; }
.star:nth-child(4n + 4) { top: 65%; left: 40%; --shimmer-delay: 3s; }
.star:nth-child(4n + 5) { top: 35%; left: 80%; --shimmer-delay: 0.5s; }
.star:nth-child(4n + 6) { top: 55%; left: 65%; --shimmer-delay: 1.5s; }
.star:nth-child(4n + 7) { top: 75%; left: 85%; --shimmer-delay: 2.5s; }
.star:nth-child(4n + 8) { top: 85%; left: 15%; --shimmer-delay: 3.5s; }
/* Added more star positions */
.star:nth-child(4n + 9) { top: 10%; left: 50%; --shimmer-delay: 0.2s; }
.star:nth-child(4n + 10) { top: 20%; left: 70%; --shimmer-delay: 1.2s; }
.star:nth-child(4n + 11) { top: 50%; left: 5%; --shimmer-delay: 2.2s; }
.star:nth-child(4n + 12) { top: 70%; left: 60%; --shimmer-delay: 3.2s; }
.star:nth-child(4n + 13) { top: 30%; left: 90%; --shimmer-delay: 0.8s; }
.star:nth-child(4n + 14) { top: 60%; left: 25%; --shimmer-delay: 1.8s; }
.star:nth-child(4n + 15) { top: 80%; left: 75%; --shimmer-delay: 2.8s; }
.star:nth-child(4n + 16) { top: 90%; left: 30%; --shimmer-delay: 3.8s; }


@keyframes shimmer {
    0%, 100% { 
        opacity: 0.2;
        box-shadow: 0 0 2px var(--text-primary);
    }
    50% { 
        opacity: 1;
        box-shadow: 0 0 8px var(--text-primary), 0 0 12px var(--neon-blue);
    }
}

.hero h1 {
    font-size: 4em;
    font-weight: 800;
    margin-bottom: 0.5em;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
}

.hero p {
    font-size: 1.4em;
    color: var(--text-secondary); /* Fix: Changed from --text-color-secondary to --text-secondary */
    margin-bottom: 2em;
    line-height: 1.6;
}

.cta-button {
    background: var(--gradient-primary); /* Fix: Changed from --button-gradient to --gradient-primary */
    color: var(--text-primary);
    padding: 15px 40px;
    border-radius: 30px;
    font-size: 1.2em;
    font-weight: 600;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow-button); /* Fix: Changed from --button-shadow to --shadow-button */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine); /* Fix: Changed from --button-shine to --gradient-shine */
    transition: left 0.6s ease;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-button-hover); /* Fix: Changed to use the hover shadow variable */
    text-shadow: 
        0 0 8px rgba(255, 255, 255, 0.6),
        0 0 12px rgba(var(--neon-blue-rgb), 0.4);
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:active {
    transform: translateY(-1px);
    box-shadow: var(--shadow-button);
}

/* Quick Access Section */
.quick-access {
    margin: -10px auto 0;
    text-align: center;
    max-width: 1200px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--text-primary);
}

.quick-access h2 {
    font-size: 2.2em;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
}

.quick-links {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: center;
    padding: 1rem;
}

.quick-link {
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-decoration: none;
    color: var(--text-primary); /* Changed from --text-color */
    font-size: 1.8rem;
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 16px;
    padding: 2rem;
    min-width: 200px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.quick-link:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 
        0 8px 25px rgba(var(--neon-blue-rgb), 0.2),
        0 8px 25px rgba(var(--cosmic-pink-rgb), 0.2);
}

.quick-link img {
    width: 70px;
    height: 70px;
    object-fit: contain;
    transition: transform 0.3s ease;
    filter: drop-shadow(0 0 8px rgba(var(--neon-blue-rgb), 0.3));
}

.quick-link:hover img {
    transform: scale(1.15) rotate(5deg);
}

.quick-link span {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary); /* Changed from --text-color */
    text-align: center;
    transition: all 0.3s ease;
}

.quick-link:hover span {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}



/* Card Styles */

/* New Unified Card Styles */
.card {
  background: rgba(255, 255, 255, 0.05); /* subtle translucent background */
  border-radius: 12px;
  padding: 0; /* Removed padding to match Explore page */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Smoother animation */
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  margin: 0;
  position: relative;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
  border-color: rgba(var(--neon-blue-rgb), 0.2);
  background: rgba(255, 255, 255, 0.08);
}

/* Add bottom gradient line on hover */
.card::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
  opacity: 0.7;
  z-index: 1;
}

.card:hover::before {
  transform: scaleX(1);
}

/* Card Content Styles */
.card-content {
  padding: 12px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  text-align: center;
}

.text-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.text-content h3 {
  font-size: 0.9rem;
  margin: 0 0 4px 0;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90%;
}

.text-content p {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin: 0;
  max-width: 90%;
}

/* Card heading and paragraph styles */
.card h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
  color: #ffffff;
}

.card p {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 12px;
}

.card .button,
.card button,
.card a.button {
  margin-top: auto; /* Push button to bottom of card */
  display: inline-block;
  background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
  color: #fff;
  padding: 8px 16px;
  border: none;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-shadow: 0 0 10px rgba(var(--neon-blue-rgb), 0.5);
  text-align: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.card .button:hover,
.card button:hover,
.card a.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
  text-shadow: 
        0 0 10px rgba(var(--neon-blue-rgb), 0.8),
        0 0 20px rgba(var(--cosmic-pink-rgb), 0.4);
}

/* Card with image */
.card .img-container {
  width: 100%;
  height: 160px;
  position: relative;
  overflow: hidden;
  border-radius: 0;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
}

.card .img-container img {
  width: 85%;
  height: 85%;
  display: block;
  transition: transform 0.8s cubic-bezier(0.19, 1, 0.22, 1);
  object-fit: contain;
  object-position: center;
  filter: brightness(0.9);
}

.card:hover .img-container img {
  transform: scale(1.08);
  filter: brightness(1.1) contrast(1.1);
}

/* Play Button & Overlay */
.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.card:hover .play-overlay {
  opacity: 1;
}

.play-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.play-button i {
    font-size: 1rem;
    /* margin-left: 2px; Removed to allow flexbox to perfectly center the icon */
    color: white;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.play-button:hover {
    transform: scale(1.1);
    box-shadow: 
        0 0 30px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(var(--neon-blue-rgb), 0.4);
}

.play-button:hover i {
    text-shadow: 
        0 0 10px rgba(255, 255, 255, 0.8),
        0 0 20px rgba(var(--neon-blue-rgb), 0.4);
}

/* Card Section Styles */
.section .cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 24px;
  padding: 0 10px;
}

.section .section-description {
  color: rgba(255, 255, 255, 0.7);
  max-width: 800px;
  margin: 0 auto 24px;
  text-align: center;
  font-size: 1.1em;
  line-height: 1.6;
}

/* Specific section styles */
.trending .cards,
.featured-artists .cards,
.new-releases .cards {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Fix for card height consistency */
.section .cards {
  display: grid;
  align-items: stretch;
}

/* Button margin-top is now handled in the main button styles for .card .button */
/* Comments about moved styles (home.css, variables.css) can be removed if those files are not in use or integrated */


/* Trending Section Styles */
.trending,
.featured-artists,
.new-releases,
.recommended {
    padding: 4rem 0;
    background: linear-gradient(180deg, rgba(19, 21, 26, 0.8) 0%, rgba(26, 29, 36, 0.8) 100%);

    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.section-header {
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
}

.section-header h2 {
    font-size: 2.2em;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
}

/* Enhanced Carousel Styles */
.carousel-container {
    position: relative;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    overflow: hidden; /* Changed from visible to hidden */
    padding: 1rem 60px;
    opacity: 1; /* Changed from 0 to ensure visibility */
}

.carousel-track {
    display: flex;
    transition: transform 0.5s cubic-bezier(0.3, 0, 0.3, 1);
    gap: 30px;
    padding: 1rem 0;
    will-change: transform;
    margin-left: auto; /* Added for centering */
    margin-right: auto; /* Added for centering */
}

.carousel-card {
    flex: 0 0 280px; /* Changed to use flex-basis */
    width: 280px; /* Added explicit width */
    max-width: 280px;
    transition: transform 0.5s cubic-bezier(0.3, 0, 0.3, 1), opacity 0.3s ease;
}

/* Ensure card content is visible */
.carousel-card .card {
    background: rgba(255, 255, 255, 0.05);
    height: 100%;
    width: 100%;
    visibility: visible;
    opacity: 1;
}

/* Adjust navigation buttons */
.carousel-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%) scale(0.95);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 46px;
    height: 46px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
    z-index: 100; /* Increased z-index to ensure visibility */
    opacity: 0.8;
}

.carousel-button:hover {
    background: var(--gradient-primary); /* Changed from --button-gradient to --gradient-primary */
    transform: translateY(-50%) scale(1.1);
    box-shadow: 
        0 0 20px rgba(var(--neon-blue-rgb), 0.3),
        0 0 40px rgba(var(--cosmic-pink-rgb), 0.2);
}

.carousel-button:active {
    transform: translateY(-50%) scale(0.95);
}

.carousel-button.prev {
    left: 15px;
}

.carousel-button.next {
    right: 15px;
}

.carousel-button::before {
    content: '';
    width: 10px;
    height: 10px;
    border-top: 2px solid white;
    border-right: 2px solid white;
    display: block;
    transition: border-color 0.3s ease;
    transform: rotate(45deg) scale(1.2);
}

.carousel-button.prev::before {
    transform: rotate(-135deg) scale(1.2);
}

.carousel-button.next::before {
    transform: rotate(45deg) scale(1.2);
}

/* Loading state for carousel images */
.img-container img {
    /* ...existing code... */
    opacity: 0;
    animation: imageLoad 0.3s ease forwards;
}

@keyframes imageLoad {
    from { 
        opacity: 0;
        transform: scale(0.95);
    }
    to { 
        opacity: 1;
        transform: scale(1);
    }
}

/* Improved card transitions */
.carousel-card {
    /* ...existing code... */
    transition: transform 0.5s cubic-bezier(0.3, 0, 0.3, 1),
                opacity 0.3s ease;
}

.carousel-card.is-transitioning {
    opacity: 0.5;
    pointer-events: none;
}

/* Enhanced hover effects */
.carousel-card:hover .card {
    transform: translateY(-8px) scale(1.02);
}

/* Card loading skeleton */
.card-loading {
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 37%,
        rgba(255, 255, 255, 0.05) 63%
    );
    background-size: 400% 100%;
    animation: cardLoading 1.4s ease infinite;
}

@keyframes cardLoading {
    from {
        background-position: 100% 50%;
    }
    to {
        background-position: 0 50%;
    }
}

/* Mini Player Styles */
.mini-player {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(19, 21, 26, 0.95), rgba(26, 29, 36, 0.95));
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    z-index: 1001;
    padding: 12px 20px;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.mini-player.hidden {
    transform: translateY(100%);
}

.mini-player.hidden:not(.show) {
    display: none;
}

.mini-player-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    gap: 20px;
}

.mini-player-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.mini-player-artwork {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.mini-player-text {
    min-width: 0;
    flex: 1;
}

.mini-player-text h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 2px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mini-player-text p {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mini-player-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 0 0 auto;
}

.mini-control-btn {
    background: transparent;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.mini-control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--neon-blue);
}

.play-pause-btn {
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    color: white;
    width: 40px;
    height: 40px;
}

.play-pause-btn:hover {
    background: linear-gradient(135deg, var(--cosmic-pink), var(--neon-blue));
    transform: scale(1.05);
}

.mini-player-progress {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 2;
    min-width: 200px;
}

.progress-bar {
    flex: 1;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    cursor: pointer;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    border-radius: 2px;
    width: 30%;
    transition: width 0.1s ease;
}

.time-display {
    display: flex;
    gap: 8px;
    font-size: 0.75rem;
    color: var(--text-secondary);
    white-space: nowrap;
}

.mini-player-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex: 0 0 auto;
}

/* Footer Styles */
footer {
    background: var(--header-gradient-start);
    padding: 2rem;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin-top: 4rem;
    margin-bottom: 80px; /* Add space for mini player */
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    color: var(--text-secondary);
}










/* Responsive styles for cards */
@media (max-width: 1024px) {
  .section .cards {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
  }

}

@media (max-width: 768px) {
  .section .cards {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }

  .card {
    /* Padding is now handled by card-content */
  }

  .card-content {
    padding: 10px;
  }

  .card h3,
  .text-content h3 {
    font-size: 1.1em;
  }

  .card p,
  .text-content p {
    font-size: 0.9em;
  }

}

@media (max-width: 480px) {
  .section .cards {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 12px;
  }

  .card {
    /* Padding is now handled by card-content */
  }

  .card-content {
    padding: 8px;
  }

  .card .img-container {
    height: 140px;
  }

  .card h3,
  .text-content h3 {
    font-size: 1em;
    margin-bottom: 6px;
  }

  .card p,
  .text-content p {
    font-size: 0.8em;
    margin-bottom: 8px;
  }

  .card .button,
  .card button,
  .card a.button {
    padding: 8px 12px;
    font-size: 0.9em;
  }

}

@media (min-width: 1441px) {
    .hero-content {
        max-width: 1200px;
    }
    
    .hero h1 {
        font-size: 5em;
    }
    .carousel-container {
        max-width: 1600px;
    }
}

@media (max-width: 1024px) {
    .hero {
        min-height: 90vh;
    }
    
    .section .cards {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
    
    .carousel-container {
        padding: 1rem 50px;
    }
}

@media (max-width: 768px) {
    /* Header styles */
    header {
        padding: 1rem;
    }

    nav {
        flex-direction: column;
        gap: 1rem;
    }

    .logo img {
        width: 60px;
        height: 60px;
    }

    .menu {
        flex-direction: column;
        align-items: center;
    }

    /* Hero styles */
    .hero {
        min-height: 80vh;
        /* Padding adjusted to ensure proper spacing for the header (100px top), 
           side margins (20px), and bottom spacing (40px) for visual balance */
        padding: 100px 20px 40px;
    }

    /* Featured Artists section responsive styles */
    .featured-artists .cards {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 12px;
    }
    
    .star {
        transform-origin: center 150%;
    }
    
    /* Quick links */
    .quick-link {
        min-width: 160px;
        padding: 1.5rem;
    }
    .quick-link img {
        width: 50px;
        height: 50px;
    }
    .quick-link span {
        font-size: 1rem;
    }
    
    /* Carousel */
    .carousel-container {
        padding: 1rem 40px;
    }
    .carousel-card {
        min-width: 240px;
    }
    .section-header h2 {
        font-size: 2em;
    }

    /* Mini Player Responsive */
    .mini-player-content {
        gap: 12px;
    }

    .mini-player-progress {
        min-width: 150px;
    }

    .mini-player-actions .mini-control-btn:last-child {
        display: none; /* Hide close button on mobile */
    }
}

@media (max-width: 480px) {
    .hero {
        min-height: 70vh;
        padding: 80px 15px 30px;
    }
    
    .hero h1 {
        font-size: 2.2em;
        margin-bottom: 0.4em;
    }
    
    .hero p {
        font-size: 1.1em;
        margin-bottom: 1.5em;
    }
    
    .star {
        transform-origin: center 120%;
    }

    /* Mini Player Mobile */
    .mini-player-content {
        gap: 8px;
        padding: 0 10px;
    }

    .mini-player-info {
        gap: 8px;
        flex: 1;
        min-width: 120px;
    }

    .mini-player-artwork {
        width: 40px;
        height: 40px;
    }

    .mini-player-text h4 {
        font-size: 0.8rem;
    }

    .mini-player-text p {
        font-size: 0.7rem;
    }

    .mini-player-progress {
        min-width: 100px;
        flex: 1;
    }

    .time-display {
        font-size: 0.7rem;
        gap: 4px;
    }

    .mini-control-btn {
        width: 32px;
        height: 32px;
        padding: 6px;
    }

    .play-pause-btn {
        width: 36px;
        height: 36px;
    }

    .mini-player-actions {
        gap: 2px;
    }

    .mini-player-actions .mini-control-btn:nth-last-child(2) {
        display: none; /* Hide expand button on mobile */
    }
}
