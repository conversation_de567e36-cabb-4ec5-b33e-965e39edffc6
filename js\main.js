class Carousel {
    constructor(container) {
        if (!container) {
            throw new Error('Carousel container element is required');
        }

        this.container = container;
        this.track = container.querySelector('.carousel-track');
        this.cards = this.track?.children;
        
        if (!this.track || !this.cards?.length) {
            throw new Error('Carousel requires a track element with child items');
        }

        this.nextButton = container.querySelector('.carousel-button.next');
        this.prevButton = container.querySelector('.carousel-button.prev');

        if (!this.nextButton || !this.prevButton) {
            throw new Error('Carousel navigation buttons not found');
        }

        this.cardWidth = this.cards[0].offsetWidth + 30; // Including gap
        this.currentIndex = 0;
        this.isAnimating = false;
        this.isLoading = true;
        this.loadedImages = 0;
        this.totalImages = this.cards.length;
        
        this.init();
    }

    init() {
        this.preloadImages();
        this.nextButton.addEventListener('click', () => this.handleNavigation('next'));
        this.prevButton.addEventListener('click', () => this.handleNavigation('prev'));
        this.updateButtonsState();

        // Add touch support
        this.setupTouchEvents();
        
        // Add keyboard navigation
        this.setupKeyboardEvents();
        
        // Update on window resize
        this.setupResizeHandler();

        // Add intersection observer
        this.setupLazyLoading();
        
        // Smooth scrolling is handled by CSS transitions on .carousel-track
        // this.setupSmoothScrolling(); 
    }

    preloadImages() {
        Array.from(this.cards).forEach(card => {
            const img = card.querySelector('img');
            if (img) {
                if (img.complete) {
                    this.handleImageLoad();
                } else {
                    img.addEventListener('load', () => this.handleImageLoad());
                }
            }
        });
    }

    handleImageLoad() {
        this.loadedImages++;
        if (this.loadedImages === this.totalImages) {
            this.isLoading = false;
            this.container.style.opacity = '1';
        }
    }

    setupLazyLoading() {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const card = entry.target;
                        card.classList.remove('card-loading');
                        observer.unobserve(card);
                    }
                });
            },
            { threshold: 0.1 }
        );

        this.cards.forEach(card => observer.observe(card));
    }

    // setupSmoothScrolling() {
    //     let animationFrame;
    //     let targetPosition;
    //     let currentPosition = 0;
    //
    //     const animate = () => {
    //         if (!targetPosition) return;
    //
    //         const diff = targetPosition - currentPosition;
    //         const delta = diff * 0.1;
    //
    //         if (Math.abs(delta) < 0.5) {
    //             currentPosition = targetPosition;
    //             targetPosition = null;
    //         } else {
    //             currentPosition += delta;
    //         }
    //
    //         this.track.style.transform = `translateX(${-currentPosition}px)`;
    //         
    //         if (targetPosition !== null) {
    //             animationFrame = requestAnimationFrame(animate);
    //         }
    //     };
    //
    //     this.smoothScrollTo = (position) => {
    //         targetPosition = position;
    //         cancelAnimationFrame(animationFrame);
    //         animationFrame = requestAnimationFrame(animate);
    //     };
    // }

    handleNavigation(direction) {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        if (direction === 'next') {
            this.next();
        } else {
            this.prev();
        }
        setTimeout(() => this.isAnimating = false, 500);
    }

    next() {
        if (this.currentIndex < this.cards.length - this.getVisibleCards()) {
            this.currentIndex++;
            this.updatePosition();
        }
    }

    prev() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            this.updatePosition();
        }
    }

    getVisibleCards() {
        return Math.floor(this.container.offsetWidth / this.cardWidth);
    }

    // calculateInitialPosition() {
    //     const containerWidth = this.container.offsetWidth;
    //     const totalCards = this.cards.length;
    //     const cardWidth = this.cardWidth;
    //     const visibleCards = Math.floor(containerWidth / cardWidth);
    //     
    //     // Center the carousel by adjusting the starting position
    //     const offset = (containerWidth - (visibleCards * cardWidth)) / 2;
    //     this.track.style.paddingLeft = `${offset}px`;
    // }

    updatePosition() {
        const position = this.currentIndex * this.cardWidth;
        this.track.style.transform = `translateX(${-position}px)`;
        this.updateButtonsState();
    }

    updateButtonsState() {
        this.prevButton.style.opacity = this.currentIndex === 0 ? '0.5' : '1';
        this.prevButton.style.pointerEvents = this.currentIndex === 0 ? 'none' : 'auto';
        
        const maxIndex = this.cards.length - this.getVisibleCards();
        this.nextButton.style.opacity = this.currentIndex >= maxIndex ? '0.5' : '1';
        this.nextButton.style.pointerEvents = this.currentIndex >= maxIndex ? 'none' : 'auto';
    }

    setupTouchEvents() {
        let startX, moveX;
        const threshold = 50;

        this.track.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        }, { passive: true });

        this.track.addEventListener('touchmove', (e) => {
            moveX = e.touches[0].clientX;
        }, { passive: true });

        this.track.addEventListener('touchend', () => {
            if (!startX || !moveX) return;
            
            const diff = startX - moveX;
            if (Math.abs(diff) > threshold) {
                if (diff > 0) this.handleNavigation('next');
                else this.handleNavigation('prev');
            }
            
            startX = null;
            moveX = null;
        });
    }

    setupKeyboardEvents() {
        this.container.setAttribute('tabindex', '0');
        this.container.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') this.handleNavigation('prev');
            if (e.key === 'ArrowRight') this.handleNavigation('next');
        });
    }

    setupResizeHandler() {
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.cardWidth = this.cards[0].offsetWidth + 30;
                this.updatePosition();
            }, 250);
        });
    }
}

// Mini Player Class
class MiniPlayer {
    constructor() {
        this.player = document.getElementById('miniPlayer');
        this.isPlaying = false;
        this.currentTrack = null;
        this.currentTime = 0;
        this.duration = 225; // 3:45 in seconds
        this.volume = 1;

        this.elements = {
            artwork: document.getElementById('miniPlayerArtwork'),
            title: document.getElementById('miniPlayerTitle'),
            artist: document.getElementById('miniPlayerArtist'),
            playPauseBtn: document.getElementById('miniPlayPauseBtn'),
            prevBtn: document.getElementById('miniPrevBtn'),
            nextBtn: document.getElementById('miniNextBtn'),
            volumeBtn: document.getElementById('miniVolumeBtn'),
            expandBtn: document.getElementById('miniExpandBtn'),
            closeBtn: document.getElementById('miniCloseBtn'),
            progressFill: document.getElementById('miniProgressFill'),
            currentTimeDisplay: document.getElementById('miniCurrentTime'),
            durationDisplay: document.getElementById('miniDuration'),
            progressBar: this.player?.querySelector('.progress-bar')
        };

        this.init();
    }

    init() {
        if (!this.player) return;

        this.setupEventListeners();
        this.updateDurationDisplay();
        this.startProgressUpdate();
    }

    setupEventListeners() {
        // Play/Pause button
        this.elements.playPauseBtn?.addEventListener('click', () => this.togglePlayPause());

        // Previous/Next buttons
        this.elements.prevBtn?.addEventListener('click', () => this.previousTrack());
        this.elements.nextBtn?.addEventListener('click', () => this.nextTrack());

        // Volume button
        this.elements.volumeBtn?.addEventListener('click', () => this.toggleMute());

        // Expand button
        this.elements.expandBtn?.addEventListener('click', () => this.expandPlayer());

        // Close button
        this.elements.closeBtn?.addEventListener('click', () => this.hide());

        // Progress bar click
        this.elements.progressBar?.addEventListener('click', (e) => this.seekTo(e));

        // Listen for play button clicks on cards
        document.addEventListener('click', (e) => {
            if (e.target.closest('.play-button') || e.target.closest('.button')) {
                const card = e.target.closest('.card');
                if (card) {
                    this.playTrack(card);
                }
            }
        });
    }

    playTrack(card) {
        const img = card.querySelector('img');
        const title = card.querySelector('h3')?.textContent || 'Unknown Track';
        const artistInfo = card.querySelector('p')?.textContent || 'Unknown Artist';

        // Extract artist name (before the •)
        const artist = artistInfo.split('•')[0].replace('Artist:', '').trim();

        this.currentTrack = {
            title,
            artist,
            artwork: img?.src || 'imgs/album-01.png'
        };

        this.updateTrackInfo();
        this.show();
        this.play();
    }

    updateTrackInfo() {
        if (!this.currentTrack) return;

        if (this.elements.artwork) this.elements.artwork.src = this.currentTrack.artwork;
        if (this.elements.title) this.elements.title.textContent = this.currentTrack.title;
        if (this.elements.artist) this.elements.artist.textContent = this.currentTrack.artist;
    }

    togglePlayPause() {
        if (this.isPlaying) {
            this.pause();
        } else {
            this.play();
        }
    }

    play() {
        this.isPlaying = true;
        const icon = this.elements.playPauseBtn?.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-pause';
        }
    }

    pause() {
        this.isPlaying = false;
        const icon = this.elements.playPauseBtn?.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-play';
        }
    }

    previousTrack() {
        // In a real app, this would load the previous track
        console.log('Previous track');
    }

    nextTrack() {
        // In a real app, this would load the next track
        console.log('Next track');
    }

    toggleMute() {
        this.volume = this.volume > 0 ? 0 : 1;
        const icon = this.elements.volumeBtn?.querySelector('i');
        if (icon) {
            icon.className = this.volume > 0 ? 'fas fa-volume-up' : 'fas fa-volume-mute';
        }
    }

    seekTo(e) {
        const rect = this.elements.progressBar.getBoundingClientRect();
        const percent = (e.clientX - rect.left) / rect.width;
        this.currentTime = percent * this.duration;
        this.updateProgress();
    }

    updateProgress() {
        const percent = (this.currentTime / this.duration) * 100;
        if (this.elements.progressFill) {
            this.elements.progressFill.style.width = `${percent}%`;
        }
        this.updateTimeDisplay();
    }

    updateTimeDisplay() {
        if (this.elements.currentTimeDisplay) {
            this.elements.currentTimeDisplay.textContent = this.formatTime(this.currentTime);
        }
    }

    updateDurationDisplay() {
        if (this.elements.durationDisplay) {
            this.elements.durationDisplay.textContent = this.formatTime(this.duration);
        }
    }

    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    startProgressUpdate() {
        setInterval(() => {
            if (this.isPlaying && this.currentTime < this.duration) {
                this.currentTime += 1;
                this.updateProgress();
            }
        }, 1000);
    }

    expandPlayer() {
        // Navigate to the full player page
        window.location.href = 'player.html';
    }

    show() {
        if (this.player) {
            this.player.classList.add('show');
            this.player.classList.remove('hidden');
        }
    }

    hide() {
        if (this.player) {
            this.player.classList.add('hidden');
            this.player.classList.remove('show');
        }
        this.pause();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const profileButton = document.querySelector('.profile-button');
    const dropdownMenu = document.querySelector('.dropdown');

    if (profileButton && dropdownMenu) {
        profileButton.addEventListener('click', () => {
            const isExpanded = profileButton.getAttribute('aria-expanded') === 'true';
            profileButton.setAttribute('aria-expanded', !isExpanded);
            dropdownMenu.classList.toggle('show');
        });

        document.addEventListener('click', (e) => {
            if (!profileButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
                dropdownMenu.classList.remove('show');
                profileButton.setAttribute('aria-expanded', 'false');
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && dropdownMenu.classList.contains('show')) {
                dropdownMenu.classList.remove('show');
                profileButton.setAttribute('aria-expanded', 'false');
            }
        });
    }

    // Initialize all carousels with a slight delay to ensure proper rendering
    const carouselContainers = document.querySelectorAll('.carousel-container');
    if (carouselContainers.length > 0) {
        carouselContainers.forEach(container => {
            setTimeout(() => {
                new Carousel(container); // Initialize a new Carousel for each container
            }, 100);
        });
    }

    // Initialize mini player
    const miniPlayer = new MiniPlayer();

    // Test: Show mini player with sample track after 2 seconds
    setTimeout(() => {
        miniPlayer.currentTrack = {
            title: "Welcome to Banshee",
            artist: "Demo Track",
            artwork: "imgs/album-01.png"
        };
        miniPlayer.updateTrackInfo();
        miniPlayer.show();
    }, 2000);
});
