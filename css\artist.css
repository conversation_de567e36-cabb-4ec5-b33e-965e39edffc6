:root {
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    --button-gradient: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    --button-shadow: 0 5px 15px rgba(0, 224, 255, 0.08), 0 5px 15px rgba(255, 0, 110, 0.08);
    --button-hover-shadow: 0 8px 25px rgba(0, 224, 255, 0.18), 0 8px 25px rgba(255, 0, 110, 0.18);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    --transition-speed: 0.3s;
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: var(--background-secondary);
    color: var(--text-primary);
}

/* Navbar Styles */
header {
    background: var(--gradient-header);
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.menu a {
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative;
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after,
.menu a:not([aria-current="page"]):hover::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--button-gradient);
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px;
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 12px rgba(0, 224, 255, 0.3), 0 0 24px rgba(0, 224, 255, 0.2), inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--gradient-header);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    z-index: 0;
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1;
    border-radius: inherit;
}

.dropdown a:hover {
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1;
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: var(--error-color);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}


/* Container Styles */
.container {
    font-family: "Plus Jakarta Sans", Arial, sans-serif;
    letter-spacing: 1.5px;
    width: 90%;
    margin: auto;
    padding: 20px;
    margin-top: 160px;
}

.section {
    margin: 20px 0;
}

.section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    background: var(--gradient-primary);
    background-size: 200% auto;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    letter-spacing: -0.5px;
    text-align: center;
    position: relative;
    padding-bottom: 12px;
    animation: gradientFlow 8s linear infinite;
}

.section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--button-gradient);
    border-radius: 2px;
    opacity: 0.7;
}

.artist-header {
    display: flex;
    gap: 32px;
    align-items: flex-start;
    background: linear-gradient(120deg, rgba(var(--neon-blue-rgb), 0.08), rgba(var(--cosmic-pink-rgb), 0.08));
    border-radius: 24px;
    padding: 40px 32px;
    position: relative;
    overflow: hidden;
    margin-bottom: 32px;
}

.artist-header-backdrop {
    position: absolute;
    inset: 0;
    background: linear-gradient(120deg, rgba(var(--neon-blue-rgb), 0.12), rgba(var(--cosmic-pink-rgb), 0.12));
    z-index: 0;
    border-radius: 24px;
    pointer-events: none;
}

.artist-photo-container {
    position: relative;
    z-index: 1;
    width: 220px;
    height: 220px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.25);
    margin-right: 32px;
    flex-shrink: 0;
}

.artist-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    display: block;
}

.artist-photo-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(120deg, rgba(var(--neon-blue-rgb), 0.15), rgba(var(--cosmic-pink-rgb), 0.15));
    border-radius: 50%;
    pointer-events: none;
}

.artist-info {
    z-index: 1;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.artist-name h1 {
    font-size: 2.5rem;
    font-weight: 800;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 0.25em;
}

.verified-badge {
    color: var(--neon-blue);
    font-size: 1.2em;
    vertical-align: middle;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 0.5em;
}

.artist-bio-container {
    margin-bottom: 1em;
}

.artist-bio {
    color: var(--text-secondary);
    font-size: 1.05rem;
    line-height: 1.6;
}

.artist-stats {
    display: flex;
    gap: 32px;
    margin-bottom: 1em;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-icon {
    color: var(--neon-blue);
    font-size: 1.3em;
}

.stat-number {
    font-size: 1.1em;
    font-weight: 700;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.95em;
}

.artist-actions {
    display: flex;
    gap: 12px;
    margin-top: 15px;
}

.action-btn {
    padding: 12px 24px;
    border-radius: 25px;
    border: none;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 15px;
    background: var(--button-gradient);
    color: white;
    box-shadow: var(--button-shadow);
}

.action-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

.play-btn:hover {
    box-shadow: 0 8px 25px rgba(var(--neon-blue-rgb), 0.4), 0 8px 25px rgba(var(--cosmic-pink-rgb), 0.4);
}

.follow-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.follow-btn:hover {
    background: rgba(255, 255, 255, 0.15);
}

.share-btn {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.share-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Share Modal Styles */
.share-modal {
    position: fixed;
    inset: 0;
    background: rgba(0,0,0,0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.share-modal[hidden] {
    display: none !important;
}

.share-modal-content {
    background: #181a20;
    border-radius: 18px;
    padding: 2rem 2.5rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.35);
    min-width: 320px;
    max-width: 90vw;
    color: var(--text-primary);
    text-align: center;
}

.share-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.share-option {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.7rem 1.2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.7rem;
    transition: background 0.2s, transform 0.2s;
}

.share-option:hover {
    background: var(--cosmic-pink);
    transform: translateY(-2px) scale(1.03);
}

@keyframes gradientFlow {
    0% { background-position: 0% 50%; }
    100% { background-position: 200% 50%; }
}
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 15px;
}




.follow-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary); /* Fixed from --text-color */
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.follow-btn:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.share-btn {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary); /* Fixed from --text-color */
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.share-btn:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.play-btn:hover {
    box-shadow: 0 8px 25px rgba(var(--neon-blue-rgb), 0.4), 0 8px 25px rgba(var(--cosmic-pink-rgb), 0.4);
}

.action-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

/* Optional: Add minimal style for .artist-header if missing */
.artist-header {
    display: flex;
    gap: 32px;
    align-items: flex-start;
    background: linear-gradient(120deg, rgba(var(--neon-blue-rgb), 0.08), rgba(var(--cosmic-pink-rgb), 0.08));
    border-radius: 24px;
    padding: 40px 32px;
    position: relative;
    overflow: hidden;
    margin-bottom: 32px;
}

.artist-header-backdrop {
    position: absolute;
    inset: 0;
    background: linear-gradient(120deg, rgba(var(--neon-blue-rgb), 0.12), rgba(var(--cosmic-pink-rgb), 0.12));
    z-index: 0;
    border-radius: 24px;
    pointer-events: none;
}

.artist-photo-container {
    position: relative;
    z-index: 1;
    width: 220px;      /* Increased from 180px */
    height: 220px;     /* Increased from 180px */
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.25);
    margin-right: 32px;
    flex-shrink: 0;
}

.artist-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    display: block;
}

.artist-photo-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(120deg, rgba(var(--neon-blue-rgb), 0.15), rgba(var(--cosmic-pink-rgb), 0.15));
    border-radius: 50%;
    pointer-events: none;
}

.artist-info {
    z-index: 1;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.artist-name h1 {
    font-size: 2.5rem;
    font-weight: 800;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 0.25em;
}

.verified-badge {
    color: var(--neon-blue);
    font-size: 1.2em;
    vertical-align: middle;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 0.5em;
}

.artist-bio-container {
    margin-bottom: 1em;
}

.artist-bio {
    color: var(--text-secondary);
    font-size: 1.05rem;
    line-height: 1.6;
}

.artist-stats {
    display: flex;
    gap: 32px;
    margin-bottom: 1em;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-icon {
    color: var(--neon-blue);
    font-size: 1.3em;
}

.stat-number {
    font-size: 1.1em;
    font-weight: 700;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.95em;
}


/* Track List */
.track-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin: 2rem 0;
}

.track-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--card-bg);
    border: var(--card-border);
    border-radius: var(--card-radius);
}

.track-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-muted);
    width: 30px;
}

.track-info {
    flex: 1;
}

.track-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.track-meta {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.track-duration {
    color: var(--text-muted);
}

/* Albums Section */
.albums-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--card-spacing);
    margin: var(--section-spacing) 0;
}

.album-card {
    background: var(--card-bg);
    border: var(--card-border);
    border-radius: var(--card-radius);
    overflow: hidden;
}

.album-artwork {
    aspect-ratio: 1;
    width: 100%;
    object-fit: cover;
}

.album-info {
    padding: 15px;
}

.album-year {
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* Similar Artists Section */
.similar-artists {
    margin: 4rem 0;
    padding: 2rem 0;
}

.similar-artists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.similar-artist-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: slideUp 0.5s ease forwards;
    opacity: 0;
    animation-delay: calc(var(--item-index, 0) * 0.1s);
}

.similar-artist-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--button-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
    opacity: 0.7;
}

.similar-artist-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2), 0 0 20px rgba(var(--neon-blue-rgb), 0.15);
}

.similar-artist-card:hover::before {
    transform: scaleX(1);
}

.similar-artist-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.similar-artist-card:hover .similar-artist-photo {
    border-color: rgba(var(--neon-blue-rgb), 0.3);
    box-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.2);
}

.similar-artist-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.similar-artist-card:hover .similar-artist-photo img {
    transform: scale(1.1);
}

.similar-artist-card h3 {
    font-size: 1.2rem;
    margin: 0.5rem 0;
    font-weight: 600;
    transition: color 0.3s ease;
}

.similar-artist-card:hover h3 {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.similar-artist-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.view-artist-btn {
    background: var(--button-gradient);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: auto;
    box-shadow: var(--button-shadow);
}

.view-artist-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

.view-artist-btn:active {
    transform: translateY(-1px);
    box-shadow: var(--button-shadow);
}

.similar-artist-card.clicked {
    animation: pulse 0.3s ease;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes gradientFlow {
    0% { background-position: 0% 50%; }
    100% { background-position: 200% 50%; }
}

@keyframes shimmer {
    0% {
        background-position: 0% 0%;
    }
    25% {
        background-position: 50% 100%;
    }
    50% {
        background-position: 100% 50%;
    }
    75% {
        background-position: 50% 0%;
    }
    100% {
        background-position: 0% 0%;
    }
}

.artist-carousel {
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: 200px;
    gap: var(--card-spacing);
    overflow-x: auto;
    padding: 20px 0;
    scroll-snap-type: x mandatory;
    /* scrollbar-width: none; */ /* Commented out due to compatibility issues */
}

.artist-carousel::-webkit-scrollbar {
    display: none;
}

.related-artist-card {
    scroll-snap-align: start;
    background: var(--card-bg);
    border: var(--card-border);
    border-radius: var(--card-radius);
    overflow: hidden;
}

/* Share Modal Styles */
.share-modal {
    position: fixed;
    inset: 0;
    background: rgba(0,0,0,0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.share-modal[hidden] {
    display: none !important;
}

.share-modal-content {
    background: #181a20;
    border-radius: 18px;
    padding: 2rem 2.5rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.35);
    min-width: 320px;
    max-width: 90vw;
    color: var(--text-primary);
    text-align: center;
}

.share-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.share-option {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.7rem 1.2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.7rem;
    transition: background 0.2s, transform 0.2s;
}

.share-option:hover {
    background: var(--cosmic-pink);
    transform: translateY(-2px) scale(1.03);
}
