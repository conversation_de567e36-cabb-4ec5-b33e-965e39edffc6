// Subscribe Page Logic
// Handles plan selection, saves to sessionStorage, and redirects to payment.html

document.addEventListener('DOMContentLoaded', function() {
    const planButtons = document.querySelectorAll('.plan-card .button');

    planButtons.forEach(button => {
        button.addEventListener('click', function() {
            const planType = button.getAttribute('data-plan');
            let plan = {};

            if (planType === 'free') {
                plan = {
                    name: 'Free Account',
                    price: 0.00,
                    interval: 'month',
                    features: [
                        'Enjoy Banshee with occasional ads',
                        'Basic audio quality (128kbps)',
                        'Limited skips (6 per hour)',
                        'Mobile app access'
                    ]
                };
            } else if (planType === 'premium') {
                plan = {
                    name: 'Premium',
                    price: 2.99,
                    interval: 'month',
                    features: [
                        'Ad-free listening experience',
                        'High-quality audio (320kbps)',
                        'Unlimited skips',
                        'Offline mode',
                        'Cross-platform sync',
                        'Exclusive content access'
                    ]
                };
            } else if (planType === 'artist') {
                plan = {
                    name: 'Artist Account',
                    price: 4.99,
                    interval: 'month',
                    features: [
                        'All Premium features included',
                        'Upload unlimited tracks',
                        'Advanced analytics dashboard',
                        'Promotional tools',
                        'Direct fan engagement',
                        'Custom artist profile'
                    ]
                };
            }

            // Save selected plan to sessionStorage
            sessionStorage.setItem('selectedPlan', JSON.stringify(plan));

            // Redirect to payment page (skip for free plan)
            if (planType === 'free') {
                // Optionally, redirect to a welcome or dashboard page for free users
                window.location.href = 'index.html';
            } else {
                window.location.href = 'payment.html';
            }
        });
    });
});
