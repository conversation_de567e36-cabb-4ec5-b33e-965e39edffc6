:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: #121212;
    color: var(--text-primary); /* Fixed: use --text-primary */
}

/* Navbar Styles */
header {
     background: linear-gradient(
        90deg,
        rgba(19, 21, 26, 0.95) 0%,
        rgba(26, 29, 36, 0.92) 60%,
        rgba(27, 27, 27, 0.1) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    /* width: 100%; */ /* Redundant */
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;    
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative; /* For ::after positioning */
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative; /* For pseudo-element positioning */
    overflow: hidden;   /* To clip the pseudo-element with border-radius */
    z-index: 0;         /* Establish stacking context for ::before z-index */
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1; /* Place background behind the text */
    border-radius: inherit; /* Inherit parent's border-radius */
}

.dropdown a:hover {
    /* Background is now handled by ::before pseudo-element */
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1; /* Fade in the background */
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a; /* Or use your --error-color variable: var(--error-color); */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}



/* Core Layout - Updated to match Home/Explore */
.content-wrapper {
    padding: 32px;
    height: calc(100vh - 160px);
    overflow-y: auto;
    background: var(--background-color);
}

/* Playlist Header - Enhanced with gradient background */
.playlist-header {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 32px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.04);
    border-radius: 16px;
    margin-bottom: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    position: relative;
    overflow: hidden;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed), border-color var(--transition-speed);
}

.playlist-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.12);
}

/* Add gradient overlay to match Profile page */
.playlist-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 50%,
        rgba(var(--electric-violet-rgb), 0.05) 100%
    );
    z-index: 0;
    pointer-events: none;
}

.playlist-cover {
    position: relative;
    width: 280px;
    height: 280px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(0, 224, 255, 0.4); /* Changed to neon-blue */
    z-index: 1;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.playlist-cover:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 25px rgba(0, 0, 0, 0.4);
}

.playlist-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.edit-cover {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    transition: opacity var(--transition-speed);
    cursor: pointer;
    border: none;
    color: white;
}

.edit-cover i {
    font-size: 24px;
    color: var(--neon-blue);
}

.edit-cover span {
    font-size: 14px;
    font-weight: 600;
}

.edit-cover:hover {
    opacity: 1;
}

.playlist-info {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    gap: 16px;
    background: rgba(255, 255, 255, 0.03); /* Match profile page style */
    padding: 24px;
    border-radius: 12px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    z-index: 1; /* Ensure it's above the gradient overlay */
    transition: background var(--transition-speed), border-color var(--transition-speed);
}

.playlist-header:hover .playlist-info {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.1);
}

.playlist-type {
    font-size: 14px;
    font-weight: 600;
    color: var(--neon-blue);
    letter-spacing: 1px;
}

#playlist-name {
    font-size: 48px;
    font-weight: 800;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.2;
    transition: transform var(--transition-fast);
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.playlist-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-secondary);
    font-size: 14px;
}

.creator-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
}

.dot {
    font-size: 8px;
    vertical-align: middle;
}

.playlist-actions {
    display: flex;
    gap: 16px;
    margin-top: 24px;
}

/* Button Styles - Updated to match Home/Explore */
.play-all, .shuffle-play, .more-options {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 25px; /* Updated to match Explore page */
    font-weight: 600;
    font-size: 15px;
    transition: all var(--transition-speed);
    border: none;
    cursor: pointer;
    text-transform: uppercase; /* Added to match Explore page */
    letter-spacing: 1px; /* Added to match Explore page */
}

.play-all {
    background: var(--button-gradient);
    color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.play-all:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
}

.play-all:active, .shuffle-play:active, .more-options:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

.shuffle-play {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 120px; /* Added to match Explore page */
}

.shuffle-play:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 224, 255, 0.1);
}

.share-playlist {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 12px 24px;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed);
}

.share-playlist:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 224, 255, 0.1);
}

.more-options {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.more-options:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Songs Section - Updated to match Library page */
.songs-section {
    margin-top: 24px;
    background: rgba(20, 30, 45, 0.3);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.section-header h2 {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

/* Songs Table - Enhanced with better styling */
.songs-table {
    background: rgba(30, 41, 59, 0.3);
    border-radius: 12px;
    overflow: hidden;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: var(--shadow-md);
}

.songs-header {
    display: grid;
    grid-template-columns: 60px 3fr 2fr 2fr 80px;
    padding: 16px 24px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.header-cell {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.song-item {
    display: grid;
    grid-template-columns: 60px 3fr 2fr 2fr 80px;
    padding: 12px 24px;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.03);
    transition: background var(--transition-fast);
    position: relative;
    cursor: pointer;
    gap: 16px;
}

.song-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.song-item.playing {
    background: rgba(0, 224, 255, 0.1); /* Changed to neon-blue */
    border-left: 3px solid var(--neon-blue);
    animation: playingPulse 2s infinite alternate;
}

@keyframes playingPulse {
    0% {
        background: rgba(0, 224, 255, 0.1);
    }
    100% {
        background: rgba(0, 224, 255, 0.2);
    }
}

/* Song Details Expansion */
.song-item.expanded {
    margin-bottom: 120px; /* Space for expanded details */
}

.song-details {
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 0 0 8px 8px;
    padding: 0;
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: 20px;
    height: 0;
    overflow: hidden;
    opacity: 0;
    transition: height var(--transition-speed), opacity var(--transition-speed), padding var(--transition-speed);
    z-index: 5;
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-top: none;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.song-item.expanded .song-details {
    height: 110px;
    opacity: 1;
    padding: 16px 24px;
}

.song-waveform {
    width: 100%;
    height: 60px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.song-waveform::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(var(--neon-blue-rgb), 0.2) 0%,
        rgba(var(--cosmic-pink-rgb), 0.2) 100%);
    -webkit-mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 30"><path d="M0 15 Q 10 5, 20 15 T 40 15 T 60 15 T 80 15 T 100 15" fill="none" stroke="white" stroke-width="1"/></svg>');
    -webkit-mask-size: 20% 100%;
    mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 30"><path d="M0 15 Q 10 5, 20 15 T 40 15 T 60 15 T 80 15 T 100 15" fill="none" stroke="white" stroke-width="1"/></svg>');
    mask-size: 20% 100%;
}

.song-metadata {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.metadata-row {
    display: flex;
    gap: 16px;
}

.metadata-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.metadata-label {
    font-size: 11px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metadata-value {
    font-size: 14px;
    color: var(--text-primary);
}

.preview-button {
    position: absolute;
    bottom: 16px;
    right: 24px;
    background: var(--button-gradient);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all var(--transition-speed);
}

.preview-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--neon-blue-rgb), 0.3);
}

.song-number {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.song-number input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: var(--electric-violet);
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

.song-number input[type="checkbox"]:hover,
.song-number input[type="checkbox"]:checked {
    opacity: 1;
}

.song-title-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
}

.song-cover {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
}

.song-title {
    font-weight: 600;
    color: var(--text-primary);
}

.song-artist, .song-album {
    color: var(--text-secondary);
    font-size: 14px;
}

.song-duration {
    color: var(--text-secondary);
    font-size: 14px;
    text-align: right;
}

.song-actions {
    opacity: 0;
    transition: opacity var(--transition-fast);
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.song-item:hover .song-actions {
    opacity: 1;
}

.song-action-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.song-action-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

/* Selection Toolbar */
.selection-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(var(--electric-violet-rgb), 0.15);
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(var(--electric-violet-rgb), 0.2);
}

.selection-toolbar[hidden] {
    display: none;
}

.selection-info {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--text-primary);
    font-weight: 500;
}

.selection-info input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--electric-violet);
}

.selection-actions {
    display: flex;
    gap: 12px;
}

.selection-action {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--text-primary);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all var(--transition-fast);
}

.selection-action:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.selection-action[data-action="play"] {
    background: var(--button-gradient);
    color: white;
}

.selection-action[data-action="play"]:hover {
    box-shadow: 0 4px 12px rgba(var(--neon-blue-rgb), 0.3);
}

.selection-action[data-action="remove"]:hover {
    background: rgba(var(--cosmic-pink-rgb), 0.2);
}

/* Sort Select - Updated to match other dropdowns */
.sort-select {
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border);
    border-radius: 20px;
    padding: 0 16px;
    color: var(--text-primary);
    font-family: inherit;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.sort-select:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(111, 0, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(111, 0, 255, 0.2);
}

/* Toast Notifications - Updated to match site style */
.toast {
    position: fixed;
    bottom: 24px;
    right: 24px;
    padding: 12px 20px;
    background: rgba(30, 41, 59, 0.9);
    color: var(--text-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    transform: translateY(100px);
    opacity: 0;
    transition: transform var(--transition-speed), opacity var(--transition-speed);
    z-index: 1000;
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.toast-success {
    border-left: 4px solid var(--cyber-lime);
}

.toast-info {
    border-left: 4px solid var(--neon-blue);
}

.toast-error {
    border-left: 4px solid var(--cosmic-pink);
}

/* Glow effects for interactive elements */
.playlist-name:focus {
    outline: none;
    text-shadow: 0 0 8px rgba(111, 0, 255, 0.5);
}

.song-item.playing .song-title {
    color: var(--neon-blue);
    text-shadow: 0 0 8px rgba(0, 224, 255, 0.3);
}

/* App Container and Main Content */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--background-primary);
}
.main-content {
    flex: 1 0 auto;
    width: 100%;
}

/* Analytics Section */
.analytics-section {
    margin: 2.5rem 0 2.5rem 0;
    background: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%, rgba(111,0,255,0.08) 100%);
    border-radius: 18px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.18);
    padding: 2rem 2rem 2rem 2rem;
    max-width: 1100px;
    width: 100%;
    position: relative;
    z-index: 1;
    border: 1.5px solid rgba(255,255,255,0.10);
    transition: box-shadow 0.3s, background 0.3s;
    margin-left: auto;
    margin-right: auto;
}
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 24px;
    margin-bottom: 2rem;
}
.analytics-card {
    background: rgba(255,255,255,0.05);
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.15);
    display: flex;
    align-items: center;
    gap: 1.2em;
    padding: 1.2em 1.5em;
}
.analytics-icon {
    font-size: 2.2em;
    color: var(--neon-blue);
    display: flex;
    align-items: center;
    justify-content: center;
}
.analytics-content h3 {
    font-size: 1.1em;
    margin: 0 0 0.3em 0;
    color: var(--text-primary);
}
.analytics-value {
    font-size: 1.5em;
    font-weight: 700;
    color: var(--text-primary);
}
.analytics-trend {
    font-size: 0.95em;
    margin-top: 0.2em;
}
.analytics-trend.positive {
    color: var(--cyber-lime);
}
.analytics-trend.negative {
    color: var(--cosmic-pink);
}
.top-songs-chart {
    margin-top: 2em;
}
.top-songs-chart h3 {
    font-size: 1.1em;
    margin-bottom: 1em;
    color: var(--text-primary);
}
.chart-container {
    display: flex;
    flex-direction: column;
    gap: 0.7em;
}
.chart-bar {
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    border-radius: 8px;
    padding: 0.7em 1.2em;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 1em;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    opacity: 0.92;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0; top: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.55);
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s;
}
.modal.show {
    display: flex;
}
.modal-content {
    background: #181c23;
    border-radius: 16px;
    padding: 2em 2.5em;
    box-shadow: 0 8px 32px rgba(0,0,0,0.25);
    max-width: 420px;
    width: 95vw;
    color: var(--text-primary);
    position: relative;
    animation: modalFadeIn 0.3s;
}
@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(40px);}
    to { opacity: 1; transform: translateY(0);}
}
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
}
.modal-header h2 {
    font-size: 1.3em;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}
.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.3em;
    cursor: pointer;
    transition: color 0.2s;
}
.close-btn:hover,
.close-btn:focus {
    color: var(--cosmic-pink);
    outline: none;
}
.share-options {
    display: flex;
    flex-direction: column;
    gap: 1.2em;
}
.share-link-container {
    display: flex;
    align-items: center;
    gap: 0.5em;
    width: 100%;
}
.share-link-container input[type="text"] {
    flex: 1;
    background: #23272f;
    color: var(--text-primary);
    border: 1px solid rgba(255,255,255,0.12);
    border-radius: 8px;
    padding: 0.5em 1em;
    font-size: 1em;
}
.copy-link-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.5em 1em;
    cursor: pointer;
    font-size: 1em;
    transition: background 0.2s, transform 0.2s;
}
.copy-link-btn:hover,
.copy-link-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    transform: scale(1.08);
    outline: none;
}
.share-platforms {
    display: flex;
    gap: 0.7em;
    flex-wrap: wrap;
    justify-content: flex-start;
}
.share-platform-btn {
    background: rgba(255,255,255,0.08);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.5em 1em;
    display: flex;
    align-items: center;
    gap: 0.5em;
    cursor: pointer;
    font-size: 1em;
    transition: background 0.2s, transform 0.2s;
}
.share-platform-btn:hover,
.share-platform-btn:focus {
    background: var(--gradient-primary);
    color: #fff;
    transform: translateY(-2px) scale(1.05);
    outline: none;
}
.share-platform-btn.facebook { background: #3b5998; }
.share-platform-btn.twitter { background: #1da1f2; }
.share-platform-btn.instagram { background: #e1306c; }
.share-platform-btn.whatsapp { background: #25d366; }
.share-platform-btn.facebook:hover,
.share-platform-btn.facebook:focus { background: #2d4373; }
.share-platform-btn.twitter:hover,
.share-platform-btn.twitter:focus { background: #0d95e8; }
.share-platform-btn.instagram:hover,
.share-platform-btn.instagram:focus { background: #c13584; }
.share-platform-btn.whatsapp:hover,
.share-platform-btn.whatsapp:focus { background: #128c7e; }
.embed-code-container {
    margin-top: 1em;
}
.embed-code-container h3 {
    font-size: 1em;
    color: var(--text-primary);
    margin-bottom: 0.5em;
}
.embed-code-container textarea {
    width: 100%;
    min-height: 60px;
    background: #23272f;
    color: var(--text-primary);
    border: 1px solid rgba(255,255,255,0.12);
    border-radius: 8px;
    padding: 0.5em 1em;
    resize: none;
    font-size: 0.98em;
}

/* Responsive modal/platforms/buttons */
@media (max-width: 600px) {
    .modal-content {
        padding: 1em 0.5em;
        max-width: 98vw;
    }
    .share-link-container input[type="text"] {
        font-size: 0.95em;
        padding: 0.4em 0.7em;
    }
    .copy-link-btn {
        padding: 0.4em 0.7em;
        font-size: 0.95em;
    }
    .share-platform-btn {
        font-size: 0.95em;
        padding: 0.4em 0.7em;
    }
    .embed-code-container textarea {
        font-size: 0.95em;
        padding: 0.4em 0.7em;
    }
    .modal-header h2 {
        font-size: 1.1em;
    }
}

/* Visually hidden utility */
.visually-hidden {
    position: absolute !important;
    width: 1px; height: 1px;
    padding: 0; margin: -1px;
    overflow: hidden; clip: rect(0,0,0,0);
    border: 0;
}

/* Section toolbar for description and sort */
.section-toolbar {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.2em;
    gap: 0.7em;
    width: 100%;
}

/* Generic button style for card links */
.button {
    display: inline-block;
    background: var(--gradient-primary);
    color: #fff;
    padding: 0.5em 1.3em;
    border: none;
    border-radius: 22px;
    font-weight: 600;
    font-size: 1em;
    cursor: pointer;
    text-decoration: none;
    margin-top: 0.7em;
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
    box-shadow: var(--shadow-button);
}
.button:hover, .button:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.05);
}

/* Refresh analytics button */
.refresh-analytics-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 18px;
    padding: 0.5em 1.2em;
    font-size: 1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5em;
    cursor: pointer;
    box-shadow: var(--shadow-button);
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
}
.refresh-analytics-btn:hover, .refresh-analytics-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.05);
}
